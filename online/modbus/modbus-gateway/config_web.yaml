serial:
  baud_rate: 115200
  data_bits: 8
  parity: "N"
  stop_bits: 1
  timeout_ms: 1000
  mode: RTU
master:
  device: /dev/null
slaves:
- device: /dev/null
  slave_ids:
  - 1
  - 2
  - 3
network:
  tcp_port: 1502
  web_port: 8080
  max_clients: 5
  mqtt_broker: tcp://*************:1883
  device_id: rk3566_001
  mqtt_username: kengque
  mqtt_password: Keng<PERSON>@nj
  eth1_interface:
    use_dhcp: true
    ip_address: ""
    netmask: ""
    gateway: ""
    dns1: ""
    dns2: ""
modbus:
  max_adu_length: 256
runtime:
  queue_size: 500
auth:
  enabled: true
  session_timeout: 30
  max_login_attempts: 5
  lockout_duration: 15
  require_strong_pwd: true
  allow_registration: true
  users:
  - username: admin
    password_hash: 7d9758894162accd06b244b5e474bd961bc2c90567d2b089b6ac4984f3b3637f
    salt: c8f5e6dd72146c28c61089d47ea9dcdb
    created_at: 2025-08-04T15:53:10.423831+08:00
    last_login: 0001-01-01T00:00:00Z
    is_active: true
